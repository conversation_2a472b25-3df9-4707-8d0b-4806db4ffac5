@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

/* Global styles */
@layer base {
  /* CSS Variables */
  :root {
    --custom-background: none;
  }

  /* Apply font family from Tailwind config */
  html {
    @apply font-sans antialiased;
    transition: background-color 0.5s ease, color 0.5s ease;
  }

  body {
    @apply text-dark min-h-screen;
    position: relative;
    background-color: transparent;
    transition: background-color 0.5s ease, color 0.5s ease;
  }

  /* Add transition to all elements for smooth dark mode switching */
  *, *::before, *::after {
    transition: background-color 0.5s ease, border-color 0.5s ease, color 0.3s ease, box-shadow 0.3s ease;
  }

  /* Apply background to html element to ensure it covers the entire viewport */
  html {
    background-image: var(--custom-background);
    background-size: cover;
    background-attachment: fixed;
    background-position: center;
    min-height: 100%;
  }

  /* Add a subtle overlay to ensure text readability when custom background is used */
  body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background-color: transparent;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none; /* Allow clicks to pass through */
  }

  /* Light mode overlay */
  html:not(.dark) body:not(.has-background)::before {
    background-color: rgba(255, 255, 255, 0.5);
    opacity: 0.2;
  }

  /* Dark mode overlay */
  .dark body:not(.has-background)::before {
    background-color: rgba(17, 24, 39, 0.7); /* dark:bg-gray-900 with transparency */
    opacity: 0.5;
  }

  /* Fix for background image visibility - 降低不透明度以确保背景图片可见 */
  html[style*="--custom-background:url("] + body::before,
  html.has-custom-bg + body::before,
  body.has-background::before {
    opacity: 0.3; /* 大幅降低不透明度，使背景更加可见 */
    background-color: transparent;
  }

  /* Ensure dark mode with background has transparent overlay */
  .dark body.has-background::before {
    background-color: rgba(0, 0, 0, 0.3);
    opacity: 0.3;
  }

  /* Special class for dark mode with background */
  body.dark-with-background::before {
    background-color: rgba(0, 0, 0, 0.2) !important;
    opacity: 0.2 !important;
  }

  /* Ensure body background is transparent when has background */
  body.has-background {
    background-color: transparent !important;
  }

  /* Ensure dark mode body is transparent when has background */
  .dark body.has-background {
    background-color: transparent !important;
  }

  /* Default link styles */
  a {
    @apply text-primary-500 no-underline hover:underline transition-colors duration-200;
  }

  /* Headings */
  h1 {
    @apply text-3xl font-bold mb-4 text-balance;
  }

  h2 {
    @apply text-2xl font-semibold mb-3 text-balance;
  }

  h3 {
    @apply text-xl font-semibold mb-2 text-balance;
  }

  h4 {
    @apply text-lg font-semibold mb-2;
  }

  h5 {
    @apply text-base font-semibold mb-1;
  }

  h6 {
    @apply text-sm font-semibold mb-1;
  }

  /* Paragraph spacing */
  p {
    @apply mb-4;
  }

  /* Focus styles for accessibility */
  *:focus-visible {
    @apply outline-none ring-2 ring-primary-400 ring-offset-2;
  }
}

/* Component styles */
@layer components {
  /* Button styles */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 rounded-md font-medium text-sm transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .btn-primary {
    @apply bg-primary-500 text-white hover:bg-primary-600 focus:bg-primary-600 focus:ring-primary-400;
  }

  .btn-secondary {
    @apply bg-secondary-500 text-white hover:bg-secondary-600 focus:bg-secondary-600 focus:ring-secondary-400;
  }

  .btn-outline {
    @apply border border-gray-300 bg-white text-dark hover:bg-gray-50 focus:ring-gray-400;
  }

  .btn-danger {
    @apply bg-danger-500 text-white hover:bg-danger-600 focus:bg-danger-600 focus:ring-danger-400;
  }

  .btn-success {
    @apply bg-success-500 text-white hover:bg-success-600 focus:bg-success-600 focus:ring-success-400;
  }

  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }

  .btn-lg {
    @apply px-6 py-3 text-base;
  }

  /* Form control styles */
  .form-control {
    @apply block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm
    focus:outline-none focus:ring-2 focus:ring-primary-400 focus:border-primary-400 transition-colors duration-200;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }

  .form-error {
    @apply mt-1 text-sm text-danger-500;
  }

  .form-hint {
    @apply mt-1 text-sm text-gray-500;
  }

  /* Card styles */
  .card {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden border border-gray-200 dark:border-gray-700;
  }

  .card-header {
    @apply px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/80;
  }

  .card-body {
    @apply p-6;
  }

  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/80;
  }

  /* Badge styles */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply bg-primary-100 text-primary-800;
  }

  .badge-secondary {
    @apply bg-secondary-100 text-secondary-800;
  }

  .badge-success {
    @apply bg-success-100 text-success-800;
  }

  .badge-danger {
    @apply bg-danger-100 text-danger-800;
  }

  .badge-warning {
    @apply bg-warning-100 text-warning-800;
  }

  .badge-info {
    @apply bg-info-100 text-info-800;
  }
}

/* Utility styles */
@layer utilities {
  /* Custom utilities that aren't in Tailwind by default */
  .text-balance {
    text-wrap: balance;
  }

  .text-pretty {
    text-wrap: pretty;
  }

  /* Responsive utilities */
  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Gradient text */
  .text-gradient {
    @apply bg-clip-text text-transparent;
  }
}

/* Modal-specific global styles */
body.overflow-hidden-modal {
  @apply overflow-hidden;
}
