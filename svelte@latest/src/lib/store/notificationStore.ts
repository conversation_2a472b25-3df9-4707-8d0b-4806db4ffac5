// src/lib/store/notificationStore.ts

import { writable } from 'svelte/store';

export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  message: string;
  duration?: number; // 自动消失时间（毫秒），0 表示不自动消失
  dismissible?: boolean; // 是否可手动关闭
}

interface NotificationState {
  notifications: Notification[];
}

const initialState: NotificationState = {
  notifications: []
};

const notificationWritable = writable<NotificationState>(initialState);

// 生成唯一ID
function generateId(): string {
  return `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

// 添加通知
function addNotification(
  type: Notification['type'],
  message: string,
  options: {
    duration?: number;
    dismissible?: boolean;
  } = {}
): string {
  const id = generateId();
  const notification: Notification = {
    id,
    type,
    message,
    duration: options.duration ?? (type === 'success' ? 3000 : type === 'error' ? 5000 : 4000),
    dismissible: options.dismissible ?? true
  };

  notificationWritable.update(state => ({
    ...state,
    notifications: [...state.notifications, notification]
  }));

  // 自动移除通知
  if (notification.duration && notification.duration > 0) {
    setTimeout(() => {
      removeNotification(id);
    }, notification.duration);
  }

  return id;
}

// 移除通知
function removeNotification(id: string): void {
  notificationWritable.update(state => ({
    ...state,
    notifications: state.notifications.filter(n => n.id !== id)
  }));
}

// 清除所有通知
function clearAllNotifications(): void {
  notificationWritable.update(state => ({
    ...state,
    notifications: []
  }));
}

// 便捷方法
const success = (message: string, options?: { duration?: number; dismissible?: boolean }) =>
  addNotification('success', message, options);

const error = (message: string, options?: { duration?: number; dismissible?: boolean }) =>
  addNotification('error', message, options);

const warning = (message: string, options?: { duration?: number; dismissible?: boolean }) =>
  addNotification('warning', message, options);

const info = (message: string, options?: { duration?: number; dismissible?: boolean }) =>
  addNotification('info', message, options);

export const notificationStore = {
  subscribe: notificationWritable.subscribe,
  addNotification,
  removeNotification,
  clearAllNotifications,
  success,
  error,
  warning,
  info
};
