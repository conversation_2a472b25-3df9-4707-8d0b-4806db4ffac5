<!-- src/lib/components/common/NotificationContainer.svelte -->
<script lang="ts">
  import { notificationStore, type Notification } from '$lib/store/notificationStore';
  import { fly } from 'svelte/transition';
  import { quintOut } from 'svelte/easing';

  // 直接使用store的响应式语法
  $: notificationState = $notificationStore;
  $: notifications = notificationState.notifications;

  function getNotificationClasses(type: Notification['type']): string {
    const baseClasses = 'flex items-center justify-between p-4 rounded-lg shadow-lg border max-w-md w-full';

    switch (type) {
      case 'success':
        return `${baseClasses} bg-green-50 dark:bg-green-900/20 text-green-800 dark:text-green-200 border-green-200 dark:border-green-800`;
      case 'error':
        return `${baseClasses} bg-red-50 dark:bg-red-900/20 text-red-800 dark:text-red-200 border-red-200 dark:border-red-800`;
      case 'warning':
        return `${baseClasses} bg-yellow-50 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-200 border-yellow-200 dark:border-yellow-800`;
      case 'info':
        return `${baseClasses} bg-blue-50 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200 border-blue-200 dark:border-blue-800`;
      default:
        return `${baseClasses} bg-gray-50 dark:bg-gray-900/20 text-gray-800 dark:text-gray-200 border-gray-200 dark:border-gray-800`;
    }
  }

  function getIconClasses(type: Notification['type']): string {
    switch (type) {
      case 'success':
        return 'text-green-600 dark:text-green-400';
      case 'error':
        return 'text-red-600 dark:text-red-400';
      case 'warning':
        return 'text-yellow-600 dark:text-yellow-400';
      case 'info':
        return 'text-blue-600 dark:text-blue-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  }

  function getIcon(type: Notification['type']): string {
    switch (type) {
      case 'success':
        return '✓';
      case 'error':
        return '✕';
      case 'warning':
        return '⚠';
      case 'info':
        return 'ℹ';
      default:
        return '•';
    }
  }

  function handleDismiss(id: string): void {
    notificationStore.removeNotification(id);
  }
</script>

<!-- 通知容器 -->
<div class="notification-container fixed top-4 right-4 flex flex-col gap-2 pointer-events-none">
  {#each notifications as notification (notification.id)}
    <div
      class="{getNotificationClasses(notification.type)} pointer-events-auto"
      transition:fly={{ y: -20, duration: 300, easing: quintOut }}
    >
      <!-- 图标和消息 -->
      <div class="flex items-center gap-3">
        <div class="{getIconClasses(notification.type)} text-lg font-bold">
          {getIcon(notification.type)}
        </div>
        <span class="text-sm font-medium flex-1">
          {notification.message}
        </span>
      </div>

      <!-- 关闭按钮 -->
      {#if notification.dismissible}
        <button
          type="button"
          onclick={() => handleDismiss(notification.id)}
          class="ml-3 flex-shrink-0 rounded-full p-1 hover:bg-black/10 dark:hover:bg-white/10 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-current"
          aria-label="关闭通知"
        >
          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
          </svg>
        </button>
      {/if}
    </div>
  {/each}
</div>

<style>
  /* 确保通知在所有内容之上 */
  :global(.notification-container) {
    z-index: 999999 !important;
    position: fixed !important;
    top: 1rem !important;
    right: 1rem !important;
    pointer-events: none !important;
  }

  /* 确保通知项本身可以交互 */
  :global(.notification-container > *) {
    pointer-events: auto !important;
    z-index: 999999 !important;
    position: relative !important;
  }

  /* 确保在所有可能的容器之上 */
  :global(body .notification-container) {
    z-index: 999999 !important;
  }
</style>
