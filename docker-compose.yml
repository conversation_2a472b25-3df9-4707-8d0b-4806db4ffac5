version: '3.8'

services:
  backend:
    build: ./backend
    container_name: yourworkplace-backend
    restart: unless-stopped
    volumes:
      - ./backend/instance:/app/instance
    environment:
      - FLASK_CONFIG=production
      - SECRET_KEY=${SECRET_KEY:-default_secret_key_change_in_production}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-default_jwt_secret_key_change_in_production}
    ports:
      - "5000:5000"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/v1/auth/ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  frontend:
    build: ./svelte@latest
    container_name: yourworkplace-frontend
    restart: unless-stopped
    ports:
      - "80:80"
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

volumes:
  db_data:
