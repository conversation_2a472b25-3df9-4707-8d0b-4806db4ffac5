[tool.poetry]
name = "backend"
version = "0.1.0"
description = ""
authors = ["gellar <<EMAIL>>"]
readme = "README.md"
package-mode = false

[tool.poetry.dependencies]
python = "^3.12"
flask = "^3.1.0"
requests = "^2.32.3"
flask-cors = "^5.0.1"
dotenv = "^0.9.9"
flask-sqlalchemy = "^3.1.1"
flask-migrate = "^4.1.0"
flask-bcrypt = "^1.0.1"
flask-jwt-extended = "^4.7.1"
gunicorn = "^21.2.0"
python-dotenv = "^1.1.0"


[tool.poetry.group.dev.dependencies]
pytest = "^8.3.5"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
