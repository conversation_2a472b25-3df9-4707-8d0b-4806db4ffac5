# /your_project_root/requirements.txt
# Python dependencies for the Flask backend project.
# Install using: pip install -r requirements.txt

# --- Core Flask ---
Flask>=2.0 # Use a recent stable version of Flask

# --- API & Backend Essentials ---
python-dotenv>=0.19 # For loading environment variables from .env file
Flask-Cors>=3.0     # For handling Cross-Origin Resource Sharing

# --- Database ---
Flask-SQLAlchemy>=2.5   # ORM wrapper for SQLAlchemy
Flask-Migrate>=3.1      # Database schema migrations (uses Alembic)
psycopg2-binary>=2.9    # PostgreSQL driver (use 'psycopg2' in production if possible)
# If you prefer SQLite for development, you might not need psycopg2-binary immediately,
# but you'll need it for PostgreSQL later.

# --- Authentication & Security ---
Flask-JWT-Extended>=4.3 # For handling JSON Web Tokens (JWT)
Flask-Bcrypt>=1.0       # For securely hashing passwords

# --- Data Serialization/Validation (Optional but Recommended for later) ---
# Flask-Marshmallow>=0.14 # For serializing/deserializing data
# marshmallow-sqlalchemy>=0.26 # Marshmallow integration for SQLAlchemy models

# --- External API Calls ---
requests>=2.25 # Standard library for making HTTP requests (e.g., to AI APIs)
# Add specific SDKs if needed, e.g.:
# openai>=0.27 # If using OpenAI

# --- Production Deployment (Example: Gunicorn) ---
gunicorn>=20.1 # WSGI HTTP Server for UNIX-like systems

# --- Development/Testing ---
# Add any linters, formatters, or testing tools here if desired
# Example:
pytest>=7.0
# flake8
# black
