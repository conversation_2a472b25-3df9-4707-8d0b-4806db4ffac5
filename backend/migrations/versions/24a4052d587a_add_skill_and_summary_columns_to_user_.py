"""Add skill and summary columns to user_profiles table

Revision ID: 24a4052d587a
Revises: 3fc71ac4f693
Create Date: 2025-05-11 19:04:45.784339

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '24a4052d587a'
down_revision = '3fc71ac4f693'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('user_profiles', schema=None) as batch_op:
        batch_op.add_column(sa.Column('skill', sa.Text(), nullable=True))
        batch_op.add_column(sa.Column('summary', sa.Text(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('user_profiles', schema=None) as batch_op:
        batch_op.drop_column('summary')
        batch_op.drop_column('skill')
    # ### end Alembic commands ###
