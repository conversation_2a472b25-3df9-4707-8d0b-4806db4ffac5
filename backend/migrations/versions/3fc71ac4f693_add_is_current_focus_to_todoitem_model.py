"""Add is_current_focus to TodoItem model

Revision ID: 3fc71ac4f693
Revises: 43e8f5ed2be4
Create Date: 2025-05-09 19:27:37.021248

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '3fc71ac4f693'
down_revision = '43e8f5ed2be4'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('todo_items', schema=None) as batch_op:
        batch_op.add_column(sa.Column('is_current_focus', sa.<PERSON>(), nullable=False, server_default=sa.sql.false()))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('todo_items', schema=None) as batch_op:
        batch_op.drop_column('is_current_focus')

    # ### end Alembic commands ###
