"""Add Achievement model

Revision ID: 26c16f6057ef
Revises: dfc2d03ce147
Create Date: 2025-05-07 22:20:38.788174

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '26c16f6057ef'
down_revision = 'dfc2d03ce147'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('achievements',
    sa.<PERSON>umn('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('title', sa.Text(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('quantifiable_results', sa.Text(), nullable=True),
    sa.Column('core_skills_json', sa.JSON(), nullable=True),
    sa.Column('date_achieved', sa.Date(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('achievements')
    # ### end Alembic commands ###
