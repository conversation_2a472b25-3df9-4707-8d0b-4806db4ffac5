"""Add title field to future_plans table

Revision ID: a1b2c3d4e5f6
Revises: 24a4052d587a
Create Date: 2025-05-15 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'a1b2c3d4e5f6'
down_revision = '24a4052d587a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('future_plans', schema=None) as batch_op:
        batch_op.add_column(sa.Column('title', sa.Text(), nullable=True))
    
    # Copy description to title for existing records
    op.execute("UPDATE future_plans SET title = description")
    
    # Make title not nullable after populating it
    with op.batch_alter_table('future_plans', schema=None) as batch_op:
        batch_op.alter_column('title', nullable=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('future_plans', schema=None) as batch_op:
        batch_op.drop_column('title')
    # ### end Alembic commands ###
